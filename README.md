# 自动化数据导出工具

## 版本信息
- 版本号: v1.0.0
- 变更记录: 
  - v1.0.0: 初始版本，实现绕过网站导出限制的自动化数据导出功能

## 功能简介

这是一个专门用于绕过公司网站导出限制的自动化工具。网站限制每次只能导出3个月以内的数据，本工具通过自动化操作，按2个月为单位逐步导出全年（1月到12月）的数据。

## 主要特性

- ✅ **智能时间分割**: 自动将全年数据分割为2个月的时间段（比3个月限制更安全）
- ✅ **浏览器自动化**: 连接已登录的Chrome浏览器，无需重复登录
- ✅ **智能元素识别**: 自动识别页面上的日期选择器和导出按钮
- ✅ **状态监控**: 实时监控导出进度和完成状态
- ✅ **错误重试**: 自动重试失败的导出操作
- ✅ **详细日志**: 完整的操作日志和进度报告

## 系统要求

- Python 3.7+
- Google Chrome浏览器
- Windows/macOS/Linux

## 安装步骤

1. **安装Python依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **安装Playwright浏览器**:
   ```bash
   playwright install chromium
   ```

## 使用方法

### 第一步：启动Chrome浏览器（重要！）

在使用工具之前，必须先启动Chrome浏览器并开启远程调试模式：

```bash
# Windows
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug"

# macOS
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"

# Linux
google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
```

### 第二步：登录网站

在启动的Chrome浏览器中：
1. 访问公司网站
2. 完成登录操作
3. 保持浏览器窗口打开

### 第三步：配置工具

编辑 `config.py` 文件，根据需要调整配置：

```python
# 目标网站URL
TARGET_URL = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10"

# 要导出的年份
EXPORT_YEAR = 2024

# 每次导出的月份数（建议保持为2）
MONTHS_PER_EXPORT = 2
```

### 第四步：运行工具

```bash
python main.py
```

## 工作流程

1. **连接浏览器**: 工具会自动连接到已启动的Chrome浏览器
2. **导航页面**: 自动导航到目标数据页面
3. **生成时间范围**: 按2个月为单位生成全年的时间范围列表
4. **逐个导出**: 
   - 设置时间范围
   - 触发导出操作
   - 等待导出完成
   - 处理下一个时间段
5. **生成报告**: 显示导出成功和失败的详细报告

## 时间范围示例

对于2024年，工具会生成以下时间范围：
- 2024-01-01 至 2024-02-29 (1-2月)
- 2024-03-01 至 2024-04-30 (3-4月)
- 2024-05-01 至 2024-06-30 (5-6月)
- 2024-07-01 至 2024-08-31 (7-8月)
- 2024-09-01 至 2024-10-31 (9-10月)
- 2024-11-01 至 2024-12-31 (11-12月)

## 故障排除

### 常见问题

1. **连接Chrome失败**
   - 确保Chrome已启动并开启远程调试模式
   - 检查端口9222是否被占用
   - 尝试重启Chrome浏览器

2. **找不到页面元素**
   - 网站页面结构可能已更改
   - 需要更新 `export_handler.py` 中的元素选择器

3. **导出超时**
   - 增加 `config.py` 中的 `EXPORT_WAIT_TIMEOUT` 值
   - 检查网络连接是否稳定

4. **权限问题**
   - 确保已在浏览器中完成登录
   - 检查账户是否有导出权限

### 调试模式

如需查看详细的调试信息，可以修改 `config.py` 中的日志级别：

```python
LOG_LEVEL = "DEBUG"
```

## 注意事项

- ⚠️ 请确保在使用前已在浏览器中完成登录
- ⚠️ 导出过程中请勿关闭Chrome浏览器
- ⚠️ 建议在网络稳定的环境下使用
- ⚠️ 如果网站页面结构发生变化，可能需要更新元素选择器

## 技术架构

```
main.py                 # 主程序入口和流程控制
├── time_generator.py   # 时间范围生成器
├── browser_controller.py # 浏览器控制器
├── export_handler.py   # 导出处理器
├── utils.py           # 通用工具函数
└── config.py          # 配置文件
```

## 许可证

本工具仅供学习和内部使用，请遵守相关法律法规和公司政策。
