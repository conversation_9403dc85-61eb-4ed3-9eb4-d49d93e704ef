# 配置更新说明

## 版本信息
- 版本号: v1.1.0
- 更新日期: 2025-07-31
- 变更记录: 
  - v1.1.0: 更新为2025年，每次导出1个月，优化日期输入方式

## 🔄 主要更新内容

### 1. 时间配置更新
- **目标年份**: 2024 → **2025**
- **每次导出月份数**: 2个月 → **1个月**
- **总时间段数**: 6个 → **12个**

### 2. 日期格式优化
- **日期格式**: 保持 `YYYY-MM-DD` 格式（如：`2025-02-28`）
- **输入方式**: 优先使用分离的开始/结束日期输入框
- **备选方案**: 支持多种日期选择器格式

### 3. 安全性提升
- **更安全的时间段**: 1个月远低于3个月限制
- **更高成功率**: 较短的时间段减少导出失败风险
- **更好兼容性**: 支持更多类型的日期输入控件

## 📊 新的时间范围

工具现在会生成以下12个时间段：

| 序号 | 时间段 | 日期范围 |
|------|--------|----------|
| 1 | 2025年1月 | 2025-01-01 至 2025-01-31 |
| 2 | 2025年2月 | 2025-02-01 至 2025-02-28 |
| 3 | 2025年3月 | 2025-03-01 至 2025-03-31 |
| 4 | 2025年4月 | 2025-04-01 至 2025-04-30 |
| 5 | 2025年5月 | 2025-05-01 至 2025-05-31 |
| 6 | 2025年6月 | 2025-06-01 至 2025-06-30 |
| 7 | 2025年7月 | 2025-07-01 至 2025-07-31 |
| 8 | 2025年8月 | 2025-08-01 至 2025-08-31 |
| 9 | 2025年9月 | 2025-09-01 至 2025-09-30 |
| 10 | 2025年10月 | 2025-10-01 至 2025-10-31 |
| 11 | 2025年11月 | 2025-11-01 至 2025-11-30 |
| 12 | 2025年12月 | 2025-12-01 至 2025-12-31 |

## 🔧 技术改进

### 日期输入策略优化
1. **优先策略**: 分离的开始/结束日期输入框
2. **备选策略**: 日期范围选择器
3. **兜底策略**: 通用日期输入框自动识别

### 元素选择器增强
- 增加了更多日期输入框的选择器
- 支持不同类型的Ant Design组件
- 提高了页面元素识别的成功率

## ⏱️ 性能预期

- **总时间段**: 12个（每个1个月）
- **预计总时间**: 20-60分钟（取决于网络和服务器响应）
- **单个时间段**: 约2-5分钟
- **成功率**: 预期更高（由于时间段更短）

## 🚀 使用方法

### 快速启动
```bash
# 1. 启动Chrome（调试模式）
powershell -ExecutionPolicy Bypass -File start_chrome.ps1

# 2. 在Chrome中登录网站并导航到数据页面

# 3. 运行主程序
python main.py
```

### 配置自定义
如需修改年份或月份数，编辑 `config.py`：
```python
EXPORT_YEAR = 2025  # 目标年份
MONTHS_PER_EXPORT = 1  # 每次导出月份数
```

## ✅ 验证测试

已通过以下测试：
- ✅ 时间范围生成测试
- ✅ Chrome浏览器连接测试
- ✅ 页面导航测试
- ✅ 日期输入功能测试

## 📝 重要提醒

1. **日期格式**: 确保网站接受 `YYYY-MM-DD` 格式
2. **登录状态**: 使用前请确保已在Chrome中完成登录
3. **网络稳定**: 12个时间段需要稳定的网络连接
4. **耐心等待**: 完整流程可能需要20-60分钟

## 🎯 下一步

工具已准备就绪，您现在可以：
1. 启动Chrome浏览器（调试模式）
2. 登录目标网站
3. 运行 `python main.py` 开始自动导出

---

**🎉 配置更新完成！工具现在使用2025年数据，每次导出1个月，更加安全可靠！**
