# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，提供模块功能测试

"""
模块功能测试脚本
用于验证各个模块的基本功能
"""

import asyncio
import sys
from datetime import datetime

from time_generator import TimeRangeGenerator
from browser_controller import BrowserController
from utils import setup_logger, print_colored
import config

logger = setup_logger(__name__)

async def test_time_generator():
    """测试时间范围生成器"""
    print_colored("\n=== 测试时间范围生成器 ===", "cyan")
    
    try:
        generator = TimeRangeGenerator(2024, 2)
        time_ranges = generator.generate_time_ranges()
        
        print_colored(f"生成了 {len(time_ranges)} 个时间范围:", "blue")
        for i, (start, end) in enumerate(time_ranges, 1):
            description = generator.get_range_description(start, end)
            is_valid = generator.validate_date_range(start, end)
            status = "✓" if is_valid else "✗"
            print_colored(f"  {i}. {status} {description} ({start} 至 {end})", "green" if is_valid else "red")
        
        print_colored("✓ 时间范围生成器测试通过", "green")
        return True
        
    except Exception as e:
        print_colored(f"✗ 时间范围生成器测试失败: {e}", "red")
        return False

async def test_browser_connection():
    """测试浏览器连接"""
    print_colored("\n=== 测试浏览器连接 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        # 测试连接
        if await browser_controller.connect_to_existing_chrome():
            print_colored("✓ 浏览器连接成功", "green")
            
            # 测试获取页面标题
            title = await browser_controller.get_page_title()
            print_colored(f"当前页面标题: {title}", "blue")
            
            # 测试导航
            if await browser_controller.navigate_to_target_page(config.TARGET_URL):
                print_colored("✓ 页面导航成功", "green")
                
                new_title = await browser_controller.get_page_title()
                print_colored(f"新页面标题: {new_title}", "blue")
                
                result = True
            else:
                print_colored("✗ 页面导航失败", "red")
                result = False
        else:
            print_colored("✗ 浏览器连接失败", "red")
            result = False
            
    except Exception as e:
        print_colored(f"✗ 浏览器测试失败: {e}", "red")
        result = False
    finally:
        await browser_controller.close()
    
    return result

async def test_page_elements():
    """测试页面元素识别"""
    print_colored("\n=== 测试页面元素识别 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("✗ 无法连接浏览器", "red")
            return False
        
        if not await browser_controller.navigate_to_target_page(config.TARGET_URL):
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        # 测试常见元素选择器
        selectors_to_test = [
            ("日期输入框", "input[type='date'], input[placeholder*='日期'], .ant-picker-input input"),
            ("导出按钮", "button:has-text('导出'), button:has-text('下载'), .export-btn"),
            ("确认按钮", "button:has-text('确定'), button:has-text('确认'), .ant-btn-primary"),
            ("加载指示器", ".ant-spin, .loading, [class*='loading']")
        ]
        
        found_elements = 0
        for name, selector in selectors_to_test:
            try:
                elements = await browser_controller.page.query_selector_all(selector)
                if elements:
                    print_colored(f"✓ 找到 {name}: {len(elements)} 个元素", "green")
                    found_elements += 1
                else:
                    print_colored(f"✗ 未找到 {name}", "yellow")
            except Exception as e:
                print_colored(f"✗ 测试 {name} 时出错: {e}", "red")
        
        if found_elements > 0:
            print_colored(f"✓ 页面元素识别测试完成，找到 {found_elements}/{len(selectors_to_test)} 个元素类型", "green")
            return True
        else:
            print_colored("✗ 未找到任何预期的页面元素", "red")
            return False
            
    except Exception as e:
        print_colored(f"✗ 页面元素测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

def test_configuration():
    """测试配置文件"""
    print_colored("\n=== 测试配置文件 ===", "cyan")
    
    try:
        # 检查必要的配置项
        required_configs = [
            ("TARGET_URL", config.TARGET_URL),
            ("EXPORT_YEAR", config.EXPORT_YEAR),
            ("MONTHS_PER_EXPORT", config.MONTHS_PER_EXPORT),
            ("CHROME_DEBUG_PORT", config.CHROME_DEBUG_PORT)
        ]
        
        all_valid = True
        for name, value in required_configs:
            if value is None or value == "":
                print_colored(f"✗ 配置项 {name} 未设置", "red")
                all_valid = False
            else:
                print_colored(f"✓ {name}: {value}", "green")
        
        # 验证年份
        current_year = datetime.now().year
        if not (2020 <= config.EXPORT_YEAR <= current_year + 1):
            print_colored(f"⚠️ 导出年份 {config.EXPORT_YEAR} 可能不合理", "yellow")
        
        # 验证月份数量
        if config.MONTHS_PER_EXPORT > 3:
            print_colored(f"⚠️ 每次导出月份数 {config.MONTHS_PER_EXPORT} 超过网站限制", "yellow")
        
        if all_valid:
            print_colored("✓ 配置文件验证通过", "green")
        else:
            print_colored("✗ 配置文件存在问题", "red")
        
        return all_valid
        
    except Exception as e:
        print_colored(f"✗ 配置文件测试失败: {e}", "red")
        return False

async def main():
    """主测试函数"""
    print_colored("自动化数据导出工具 - 模块测试", "cyan")
    print_colored("=" * 50, "cyan")
    
    tests = [
        ("配置文件", test_configuration),
        ("时间范围生成器", test_time_generator),
        ("浏览器连接", test_browser_connection),
        ("页面元素识别", test_page_elements)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print_colored(f"\n开始测试: {test_name}", "blue")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed_tests += 1
        except Exception as e:
            print_colored(f"✗ 测试 {test_name} 时发生异常: {e}", "red")
    
    # 测试总结
    print_colored("\n" + "=" * 50, "cyan")
    print_colored("测试总结", "cyan")
    print_colored("=" * 50, "cyan")
    print_colored(f"总测试数: {total_tests}", "blue")
    print_colored(f"通过测试: {passed_tests}", "green")
    print_colored(f"失败测试: {total_tests - passed_tests}", "red")
    print_colored(f"通过率: {passed_tests/total_tests*100:.1f}%", "blue")
    
    if passed_tests == total_tests:
        print_colored("\n🎉 所有测试通过！工具已准备就绪", "green")
        return 0
    else:
        print_colored(f"\n⚠️ {total_tests - passed_tests} 个测试失败，请检查相关问题", "yellow")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    print_colored("\n按任意键退出...", "yellow")
    input()
    sys.exit(exit_code)
