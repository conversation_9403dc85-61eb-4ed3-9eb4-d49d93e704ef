# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现数据导出处理功能

"""
导出处理器模块
负责处理数据导出的具体操作，包括设置时间范围、触发导出、监控状态
"""

import asyncio
from typing import Tuple, Optional
from datetime import datetime

from browser_controller import BrowserController
from utils import setup_logger, async_random_delay, print_colored
import config

logger = setup_logger(__name__)

class ExportHandler:
    """导出处理器类"""
    
    def __init__(self, browser_controller: BrowserController):
        """
        初始化导出处理器
        
        Args:
            browser_controller: 浏览器控制器实例
        """
        self.browser = browser_controller
        self.logger = logger
        
        # 页面元素选择器（根据实际网站HTML结构调整）
        self.selectors = {
            # 精确匹配提交时间的日期范围选择器
            "date_picker_start": "input[placeholder='开始日期'][date-range='start']",
            "date_picker_end": "input[placeholder='结束日期'][date-range='end']",
            "date_range_picker": ".ant-picker-range",
            "submitTime_container": "#submitTime",  # 提交时间容器

            # 备选日期选择器
            "alt_date_start": "input[placeholder*='开始'], input[date-range='start']",
            "alt_date_end": "input[placeholder*='结束'], input[date-range='end']",

            # 通用日期输入框（按顺序尝试）
            "date_inputs": "input[type='date'], input[placeholder*='日期'], input[placeholder*='时间'], .ant-picker input",

            # 导出按钮相关
            "export_button": "button:has-text('导出'), button:has-text('下载'), .export-btn, .download-btn",
            "confirm_button": "button:has-text('确定'), button:has-text('确认'), .ant-btn-primary",

            # 状态监控相关
            "loading_indicator": ".ant-spin, .loading, [class*='loading']",
            "success_message": ".ant-message-success, .success-message",
            "error_message": ".ant-message-error, .error-message"
        }
    
    async def set_date_range(self, start_date: str, end_date: str) -> bool:
        """
        设置日期范围

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            设置是否成功
        """
        try:
            print_colored(f"设置日期范围: {start_date} 至 {end_date}", "blue")

            # 方法1：尝试分别设置开始和结束日期（优先使用）
            if await self._set_separate_date_inputs(start_date, end_date):
                return True

            # 方法2：尝试使用日期范围选择器
            if await self._set_date_range_picker(start_date, end_date):
                return True

            # 方法3：尝试其他可能的日期输入方式
            if await self._set_alternative_date_inputs(start_date, end_date):
                return True

            print_colored("✗ 所有日期设置方法都失败了", "red")
            return False

        except Exception as e:
            self.logger.error(f"设置日期范围失败: {e}")
            return False
    
    async def _set_date_range_picker(self, start_date: str, end_date: str) -> bool:
        """使用日期范围选择器设置日期"""
        try:
            # 点击日期范围选择器
            if await self.browser.wait_for_element(self.selectors["date_range_picker"], 5000):
                await self.browser.click_element(self.selectors["date_range_picker"])
                await async_random_delay(1, 2)

                # 输入日期范围（使用单独的日期格式）
                # 尝试找到两个输入框
                inputs = await self.browser.page.query_selector_all(self.selectors["date_range_picker"] + " input")
                if len(inputs) >= 2:
                    # 如果有两个输入框，分别填充
                    await inputs[0].fill(start_date)
                    await async_random_delay(0.5, 1)
                    await inputs[1].fill(end_date)
                else:
                    # 如果只有一个输入框，尝试使用单个日期
                    await self.browser.fill_input(self.selectors["date_range_picker"] + " input", start_date)

                # 按回车确认
                await self.browser.page.keyboard.press("Enter")
                await async_random_delay(1, 2)
                
                self.logger.info("使用日期范围选择器设置成功")
                return True
                
        except Exception as e:
            self.logger.debug(f"日期范围选择器方法失败: {e}")
            
        return False
    
    async def _set_separate_date_inputs(self, start_date: str, end_date: str) -> bool:
        """分别设置开始和结束日期输入框"""
        try:
            success_count = 0

            # 方法1: 精确匹配提交时间的日期输入框
            print_colored("尝试精确匹配提交时间日期输入框...", "blue")

            # 设置开始日期
            start_selector = self.selectors["date_picker_start"]
            if await self.browser.wait_for_element(start_selector, 3000):
                print_colored(f"找到开始日期输入框: {start_selector}", "green")
                await self.browser.click_element(start_selector)
                await async_random_delay(0.5, 1)

                # 清空现有内容并输入新日期
                await self.browser.page.evaluate(f"document.querySelector(\"{start_selector}\").value = ''")
                await self.browser.fill_input(start_selector, start_date)
                await async_random_delay(0.5, 1)

                success_count += 1
                self.logger.info(f"开始日期设置成功: {start_date}")
            else:
                print_colored("未找到开始日期输入框", "yellow")

            # 设置结束日期
            end_selector = self.selectors["date_picker_end"]
            if await self.browser.wait_for_element(end_selector, 3000):
                print_colored(f"找到结束日期输入框: {end_selector}", "green")
                await self.browser.click_element(end_selector)
                await async_random_delay(0.5, 1)

                # 清空现有内容并输入新日期
                await self.browser.page.evaluate(f"document.querySelector(\"{end_selector}\").value = ''")
                await self.browser.fill_input(end_selector, end_date)
                await async_random_delay(0.5, 1)

                success_count += 1
                self.logger.info(f"结束日期设置成功: {end_date}")
            else:
                print_colored("未找到结束日期输入框", "yellow")

            # 如果精确匹配失败，尝试备选方案
            if success_count < 2:
                print_colored("尝试备选日期选择器...", "blue")

                # 备选开始日期选择器
                if success_count == 0 or not await self.browser.wait_for_element(start_selector, 1000):
                    alt_start_selector = self.selectors["alt_date_start"]
                    if await self.browser.wait_for_element(alt_start_selector, 2000):
                        await self.browser.click_element(alt_start_selector)
                        await async_random_delay(0.5, 1)
                        await self.browser.fill_input(alt_start_selector, start_date)
                        await async_random_delay(0.5, 1)
                        success_count += 1
                        self.logger.info(f"备选开始日期设置成功: {start_date}")

                # 备选结束日期选择器
                if success_count < 2:
                    alt_end_selector = self.selectors["alt_date_end"]
                    if await self.browser.wait_for_element(alt_end_selector, 2000):
                        await self.browser.click_element(alt_end_selector)
                        await async_random_delay(0.5, 1)
                        await self.browser.fill_input(alt_end_selector, end_date)
                        await async_random_delay(0.5, 1)
                        success_count += 1
                        self.logger.info(f"备选结束日期设置成功: {end_date}")

            if success_count >= 2:
                print_colored("✓ 开始和结束日期都设置成功", "green")
                self.logger.info("使用分离日期输入框设置成功")
                return True
            elif success_count == 1:
                print_colored("⚠️ 只设置了一个日期，尝试其他方法", "yellow")
                return False
            else:
                print_colored("✗ 未能设置任何日期", "red")
                return False

        except Exception as e:
            print_colored(f"✗ 分离日期输入框方法失败: {e}", "red")
            self.logger.debug(f"分离日期输入框方法失败: {e}")
            return False
    
    async def _set_alternative_date_inputs(self, start_date: str, end_date: str) -> bool:
        """尝试其他可能的日期输入方式"""
        try:
            # 查找所有可能的日期输入框
            selectors_to_try = [
                "input[type='date']",
                "input[placeholder*='日期']",
                "input[placeholder*='时间']",
                ".ant-picker input",
                "input[class*='date']",
                "input[class*='picker']"
            ]

            all_inputs = []
            for selector in selectors_to_try:
                inputs = await self.browser.page.query_selector_all(selector)
                all_inputs.extend(inputs)

            # 去重
            unique_inputs = []
            for input_elem in all_inputs:
                if input_elem not in unique_inputs:
                    unique_inputs.append(input_elem)

            if len(unique_inputs) >= 2:
                # 尝试填充前两个输入框
                await unique_inputs[0].fill(start_date)
                await async_random_delay(0.5, 1)
                await unique_inputs[1].fill(end_date)
                await async_random_delay(0.5, 1)

                self.logger.info(f"使用备选日期输入方式设置成功，找到 {len(unique_inputs)} 个输入框")
                return True
            elif len(unique_inputs) == 1:
                # 如果只有一个输入框，尝试输入开始日期
                await unique_inputs[0].fill(start_date)
                await async_random_delay(0.5, 1)

                self.logger.info("使用单个日期输入框设置开始日期")
                return True

        except Exception as e:
            self.logger.debug(f"备选日期输入方式失败: {e}")

        return False
    
    async def trigger_export(self) -> bool:
        """
        触发数据导出
        
        Returns:
            导出触发是否成功
        """
        try:
            print_colored("触发数据导出...", "blue")
            
            # 查找并点击导出按钮
            if await self.browser.wait_for_element(self.selectors["export_button"], 10000):
                await self.browser.click_element(self.selectors["export_button"])
                await async_random_delay(1, 2)

                # 如果有确认对话框，点击确认
                if await self.browser.wait_for_element(self.selectors["confirm_button"], 3000):
                    await self.browser.click_element(self.selectors["confirm_button"])
                    await async_random_delay(1, 2)
                
                print_colored("✓ 导出已触发", "green")
                self.logger.info("导出已触发")
                return True
            else:
                print_colored("✗ 找不到导出按钮", "red")
                return False
                
        except Exception as e:
            print_colored(f"✗ 触发导出失败: {e}", "red")
            self.logger.error(f"触发导出失败: {e}")
            return False
    
    async def wait_for_export_completion(self, timeout: int = 300) -> bool:
        """
        等待导出完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            导出是否成功完成
        """
        try:
            print_colored("等待导出完成...", "yellow")
            start_time = datetime.now()
            
            while (datetime.now() - start_time).seconds < timeout:
                # 检查是否有加载指示器
                loading_elements = await self.browser.page.query_selector_all(self.selectors["loading_indicator"])
                if not loading_elements:
                    # 没有加载指示器，可能已完成
                    await async_random_delay(2, 3)  # 额外等待确保完成
                    
                    # 检查是否有成功消息
                    success_elements = await self.browser.page.query_selector_all(self.selectors["success_message"])
                    if success_elements:
                        print_colored("✓ 导出成功完成", "green")
                        return True
                    
                    # 检查是否有错误消息
                    error_elements = await self.browser.page.query_selector_all(self.selectors["error_message"])
                    if error_elements:
                        print_colored("✗ 导出过程中出现错误", "red")
                        return False
                    
                    # 如果没有明确的成功/失败指示，假设成功
                    print_colored("✓ 导出可能已完成（未检测到加载状态）", "green")
                    return True
                
                # 还在加载中，继续等待
                await asyncio.sleep(5)
                print(".", end="", flush=True)  # 使用标准print显示进度点
            
            print_colored("✗ 导出等待超时", "red")
            return False
            
        except Exception as e:
            print_colored(f"✗ 等待导出完成时出错: {e}", "red")
            self.logger.error(f"等待导出完成时出错: {e}")
            return False
    
    async def export_data_for_range(self, start_date: str, end_date: str) -> bool:
        """
        为指定时间范围导出数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            导出是否成功
        """
        try:
            print_colored(f"\n开始导出数据: {start_date} 至 {end_date}", "cyan")
            
            # 1. 设置日期范围
            if not await self.set_date_range(start_date, end_date):
                return False
            
            # 2. 触发导出
            if not await self.trigger_export():
                return False
            
            # 3. 等待导出完成
            if not await self.wait_for_export_completion(config.EXPORT_WAIT_TIMEOUT):
                return False
            
            print_colored(f"✓ 时间范围 {start_date} 至 {end_date} 导出完成", "green")
            return True
            
        except Exception as e:
            print_colored(f"✗ 导出数据失败: {e}", "red")
            self.logger.error(f"导出数据失败: {e}")
            return False
