# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现数据导出处理功能

"""
导出处理器模块
负责处理数据导出的具体操作，包括设置时间范围、触发导出、监控状态
"""

import asyncio
from typing import Tuple, Optional
from datetime import datetime

from browser_controller import BrowserController
from utils import setup_logger, async_random_delay, print_colored
import config

logger = setup_logger(__name__)

class ExportHandler:
    """导出处理器类"""
    
    def __init__(self, browser_controller: BrowserController):
        """
        初始化导出处理器
        
        Args:
            browser_controller: 浏览器控制器实例
        """
        self.browser = browser_controller
        self.logger = logger
        
        # 页面元素选择器（需要根据实际网站调整）
        self.selectors = {
            # 时间选择相关
            "date_picker_start": "input[placeholder*='开始日期'], input[placeholder*='开始时间'], .ant-picker-input input",
            "date_picker_end": "input[placeholder*='结束日期'], input[placeholder*='结束时间'], .ant-picker-input:last-child input",
            "date_range_picker": ".ant-picker-range",
            
            # 导出按钮相关
            "export_button": "button:has-text('导出'), button:has-text('下载'), .export-btn, .download-btn",
            "confirm_button": "button:has-text('确定'), button:has-text('确认'), .ant-btn-primary",
            
            # 状态监控相关
            "loading_indicator": ".ant-spin, .loading, [class*='loading']",
            "success_message": ".ant-message-success, .success-message",
            "error_message": ".ant-message-error, .error-message"
        }
    
    async def set_date_range(self, start_date: str, end_date: str) -> bool:
        """
        设置日期范围
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            设置是否成功
        """
        try:
            print_colored(f"设置日期范围: {start_date} 至 {end_date}", "blue")
            
            # 方法1：尝试使用日期范围选择器
            if await self._set_date_range_picker(start_date, end_date):
                return True
            
            # 方法2：尝试分别设置开始和结束日期
            if await self._set_separate_date_inputs(start_date, end_date):
                return True
            
            # 方法3：尝试其他可能的日期输入方式
            if await self._set_alternative_date_inputs(start_date, end_date):
                return True
            
            print_colored("✗ 所有日期设置方法都失败了", "red")
            return False
            
        except Exception as e:
            self.logger.error(f"设置日期范围失败: {e}")
            return False
    
    async def _set_date_range_picker(self, start_date: str, end_date: str) -> bool:
        """使用日期范围选择器设置日期"""
        try:
            # 点击日期范围选择器
            if await self.browser.wait_for_element(self.selectors["date_range_picker"], 5000):
                await self.browser.click_element(self.selectors["date_range_picker"])
                await async_random_delay(1, 2)

                # 输入日期范围（格式可能需要调整）
                date_range = f"{start_date} ~ {end_date}"
                await self.browser.fill_input(self.selectors["date_range_picker"] + " input", date_range)

                # 按回车确认
                await self.browser.page.keyboard.press("Enter")
                await async_random_delay(1, 2)
                
                self.logger.info("使用日期范围选择器设置成功")
                return True
                
        except Exception as e:
            self.logger.debug(f"日期范围选择器方法失败: {e}")
            
        return False
    
    async def _set_separate_date_inputs(self, start_date: str, end_date: str) -> bool:
        """分别设置开始和结束日期输入框"""
        try:
            # 设置开始日期
            if await self.browser.wait_for_element(self.selectors["date_picker_start"], 5000):
                await self.browser.click_element(self.selectors["date_picker_start"])
                await async_random_delay(0.5, 1)
                await self.browser.fill_input(self.selectors["date_picker_start"], start_date)
                await async_random_delay(0.5, 1)

            # 设置结束日期
            if await self.browser.wait_for_element(self.selectors["date_picker_end"], 5000):
                await self.browser.click_element(self.selectors["date_picker_end"])
                await async_random_delay(0.5, 1)
                await self.browser.fill_input(self.selectors["date_picker_end"], end_date)
                await async_random_delay(0.5, 1)
            
            self.logger.info("使用分离日期输入框设置成功")
            return True
            
        except Exception as e:
            self.logger.debug(f"分离日期输入框方法失败: {e}")
            
        return False
    
    async def _set_alternative_date_inputs(self, start_date: str, end_date: str) -> bool:
        """尝试其他可能的日期输入方式"""
        try:
            # 查找所有可能的日期输入框
            date_inputs = await self.browser.page.query_selector_all("input[type='date'], input[placeholder*='日期'], input[placeholder*='时间']")
            
            if len(date_inputs) >= 2:
                # 假设前两个是开始和结束日期
                await date_inputs[0].fill(start_date)
                await async_random_delay(0.5, 1)
                await date_inputs[1].fill(end_date)
                await async_random_delay(0.5, 1)
                
                self.logger.info("使用备选日期输入方式设置成功")
                return True
                
        except Exception as e:
            self.logger.debug(f"备选日期输入方式失败: {e}")
            
        return False
    
    async def trigger_export(self) -> bool:
        """
        触发数据导出
        
        Returns:
            导出触发是否成功
        """
        try:
            print_colored("触发数据导出...", "blue")
            
            # 查找并点击导出按钮
            if await self.browser.wait_for_element(self.selectors["export_button"], 10000):
                await self.browser.click_element(self.selectors["export_button"])
                await async_random_delay(1, 2)

                # 如果有确认对话框，点击确认
                if await self.browser.wait_for_element(self.selectors["confirm_button"], 3000):
                    await self.browser.click_element(self.selectors["confirm_button"])
                    await async_random_delay(1, 2)
                
                print_colored("✓ 导出已触发", "green")
                self.logger.info("导出已触发")
                return True
            else:
                print_colored("✗ 找不到导出按钮", "red")
                return False
                
        except Exception as e:
            print_colored(f"✗ 触发导出失败: {e}", "red")
            self.logger.error(f"触发导出失败: {e}")
            return False
    
    async def wait_for_export_completion(self, timeout: int = 300) -> bool:
        """
        等待导出完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            导出是否成功完成
        """
        try:
            print_colored("等待导出完成...", "yellow")
            start_time = datetime.now()
            
            while (datetime.now() - start_time).seconds < timeout:
                # 检查是否有加载指示器
                loading_elements = await self.browser.page.query_selector_all(self.selectors["loading_indicator"])
                if not loading_elements:
                    # 没有加载指示器，可能已完成
                    await async_random_delay(2, 3)  # 额外等待确保完成
                    
                    # 检查是否有成功消息
                    success_elements = await self.browser.page.query_selector_all(self.selectors["success_message"])
                    if success_elements:
                        print_colored("✓ 导出成功完成", "green")
                        return True
                    
                    # 检查是否有错误消息
                    error_elements = await self.browser.page.query_selector_all(self.selectors["error_message"])
                    if error_elements:
                        print_colored("✗ 导出过程中出现错误", "red")
                        return False
                    
                    # 如果没有明确的成功/失败指示，假设成功
                    print_colored("✓ 导出可能已完成（未检测到加载状态）", "green")
                    return True
                
                # 还在加载中，继续等待
                await asyncio.sleep(5)
                print(".", end="", flush=True)  # 使用标准print显示进度点
            
            print_colored("✗ 导出等待超时", "red")
            return False
            
        except Exception as e:
            print_colored(f"✗ 等待导出完成时出错: {e}", "red")
            self.logger.error(f"等待导出完成时出错: {e}")
            return False
    
    async def export_data_for_range(self, start_date: str, end_date: str) -> bool:
        """
        为指定时间范围导出数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            导出是否成功
        """
        try:
            print_colored(f"\n开始导出数据: {start_date} 至 {end_date}", "cyan")
            
            # 1. 设置日期范围
            if not await self.set_date_range(start_date, end_date):
                return False
            
            # 2. 触发导出
            if not await self.trigger_export():
                return False
            
            # 3. 等待导出完成
            if not await self.wait_for_export_completion(config.EXPORT_WAIT_TIMEOUT):
                return False
            
            print_colored(f"✓ 时间范围 {start_date} 至 {end_date} 导出完成", "green")
            return True
            
        except Exception as e:
            print_colored(f"✗ 导出数据失败: {e}", "red")
            self.logger.error(f"导出数据失败: {e}")
            return False
