# Chrome Safe Debug Mode Startup Script v1.0.0
# 移除了不安全的启动参数，提高稳定性和安全性

Write-Host "===================================" -ForegroundColor Cyan
Write-Host "   Chrome Safe Debug Mode Startup" -ForegroundColor Cyan  
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# Create temp directory
$chromeDebugDir = "$env:TEMP\chrome_debug_auto_export_safe"
if (!(Test-Path $chromeDebugDir)) {
    New-Item -ItemType Directory -Path $chromeDebugDir -Force | Out-Null
}

Write-Host "Starting Chrome with safe remote debugging..." -ForegroundColor Yellow
Write-Host "Debug port: 9222" -ForegroundColor Blue
Write-Host "User data dir: $chromeDebugDir" -ForegroundColor Blue
Write-Host "Security: Enhanced (removed unsafe flags)" -ForegroundColor Green
Write-Host ""

# Close existing Chrome processes
Write-Host "Closing existing Chrome processes..." -ForegroundColor Yellow
Get-Process chrome -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Try multiple Chrome installation paths
$chromePaths = @(
    "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
    "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
    "$env:LOCALAPPDATA\Google\Chrome\Application\chrome.exe"
)

$chromeFound = $false
foreach ($chromePath in $chromePaths) {
    if (Test-Path $chromePath) {
        Write-Host "Found Chrome: $chromePath" -ForegroundColor Green
        
        # 只使用必要的安全参数
        $arguments = @(
            "--remote-debugging-port=9222",
            "--user-data-dir=`"$chromeDebugDir`""
        )
        
        Start-Process -FilePath $chromePath -ArgumentList $arguments
        $chromeFound = $true
        break
    }
}

if (-not $chromeFound) {
    # Try from PATH
    Write-Host "Trying to start Chrome from PATH..." -ForegroundColor Yellow
    try {
        $arguments = @(
            "--remote-debugging-port=9222",
            "--user-data-dir=`"$chromeDebugDir`""
        )
        Start-Process -FilePath "chrome.exe" -ArgumentList $arguments
        $chromeFound = $true
    }
    catch {
        Write-Host ""
        Write-Host "[ERROR] Chrome browser not found!" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please ensure Chrome is installed, or start Chrome manually:" -ForegroundColor Yellow
        Write-Host "chrome.exe --remote-debugging-port=9222 --user-data-dir=`"$chromeDebugDir`"" -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to exit"
        exit 1
    }
}

if ($chromeFound) {
    Write-Host ""
    Write-Host "[SUCCESS] Chrome started in safe debug mode" -ForegroundColor Green
    Write-Host ""
    Write-Host "Important instructions:" -ForegroundColor Cyan
    Write-Host "1. Please visit the following URL in the opened Chrome browser:" -ForegroundColor White
    Write-Host "   https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "2. Complete website login" -ForegroundColor White
    Write-Host ""
    Write-Host "3. After login, run the main program:" -ForegroundColor White
    Write-Host "   python main.py" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "4. Or run directly:" -ForegroundColor White
    Write-Host "   .\start.bat" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Note: No security warnings should appear in the browser now!" -ForegroundColor Green
    Write-Host ""

    # Wait 3 seconds for Chrome to fully start
    Write-Host "Waiting for Chrome to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3

    # Check if Chrome debug port is ready
    Write-Host "Checking Chrome debug port..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:9222/json" -TimeoutSec 5
        Write-Host "[SUCCESS] Chrome debug port is ready" -ForegroundColor Green
        Write-Host "No unsafe command line flags detected!" -ForegroundColor Green
    }
    catch {
        Write-Host "[WARNING] Chrome debug port not responding, please wait a moment" -ForegroundColor Yellow
    }

    Write-Host ""
    Read-Host "Press Enter to exit"
}
