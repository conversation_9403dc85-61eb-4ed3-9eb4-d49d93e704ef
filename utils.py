# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，提供通用工具函数

"""
通用工具函数模块
"""

import logging
import random
import time
from typing import Tuple
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

def setup_logger(name: str, level: str = "INFO") -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def random_delay(min_seconds: float, max_seconds: float) -> None:
    """
    随机延迟，避免被检测为机器人

    Args:
        min_seconds: 最小延迟秒数
        max_seconds: 最大延迟秒数
    """
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

async def async_random_delay(min_seconds: float, max_seconds: float) -> None:
    """
    异步随机延迟，避免被检测为机器人

    Args:
        min_seconds: 最小延迟秒数
        max_seconds: 最大延迟秒数
    """
    import asyncio
    delay = random.uniform(min_seconds, max_seconds)
    await asyncio.sleep(delay)

def print_colored(message: str, color: str = "white") -> None:
    """
    打印彩色文本
    
    Args:
        message: 要打印的消息
        color: 颜色名称
    """
    color_map = {
        "red": Fore.RED,
        "green": Fore.GREEN,
        "yellow": Fore.YELLOW,
        "blue": Fore.BLUE,
        "magenta": Fore.MAGENTA,
        "cyan": Fore.CYAN,
        "white": Fore.WHITE
    }
    
    color_code = color_map.get(color.lower(), Fore.WHITE)
    print(f"{color_code}{message}{Style.RESET_ALL}")

def format_time_range(start_date: str, end_date: str) -> str:
    """
    格式化时间范围显示
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        格式化的时间范围字符串
    """
    return f"{start_date} 至 {end_date}"
