# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，测试Chrome浏览器连接

"""
Chrome浏览器连接测试脚本
用于验证Chrome是否正确启动并可以连接
"""

import asyncio
import requests
import json
from browser_controller import BrowserController
from utils import print_colored

async def test_chrome_debug_port():
    """测试Chrome调试端口是否可访问"""
    print_colored("=== 测试Chrome调试端口 ===", "cyan")
    
    try:
        # 测试HTTP端点
        response = requests.get("http://localhost:9222/json", timeout=5)
        if response.status_code == 200:
            tabs = response.json()
            print_colored(f"✓ Chrome调试端口已就绪", "green")
            print_colored(f"发现 {len(tabs)} 个标签页", "blue")
            
            for i, tab in enumerate(tabs[:3]):  # 只显示前3个
                title = tab.get('title', '无标题')[:50]
                url = tab.get('url', '无URL')[:80]
                print_colored(f"  标签页 {i+1}: {title}", "white")
                print_colored(f"    URL: {url}", "white")
            
            return True
        else:
            print_colored(f"✗ Chrome调试端口响应异常: {response.status_code}", "red")
            return False
            
    except requests.exceptions.ConnectionError:
        print_colored("✗ 无法连接到Chrome调试端口 (localhost:9222)", "red")
        print_colored("请确保Chrome已以调试模式启动", "yellow")
        return False
    except Exception as e:
        print_colored(f"✗ 测试调试端口时出错: {e}", "red")
        return False

async def test_playwright_connection():
    """测试Playwright连接"""
    print_colored("\n=== 测试Playwright连接 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        if await browser_controller.connect_to_existing_chrome():
            print_colored("✓ Playwright成功连接到Chrome", "green")
            
            # 获取页面信息
            if browser_controller.page:
                title = await browser_controller.get_page_title()
                url = browser_controller.page.url
                print_colored(f"当前页面标题: {title}", "blue")
                print_colored(f"当前页面URL: {url}", "blue")
            
            return True
        else:
            print_colored("✗ Playwright连接Chrome失败", "red")
            return False
            
    except Exception as e:
        print_colored(f"✗ Playwright连接测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

def print_chrome_startup_help():
    """打印Chrome启动帮助信息"""
    print_colored("\n=== Chrome启动帮助 ===", "cyan")
    print_colored("如果连接失败，请按以下步骤操作：", "yellow")
    print_colored("", "white")
    print_colored("方法1: 使用专用启动脚本（推荐）", "green")
    print_colored("  双击运行: start_chrome.bat", "white")
    print_colored("", "white")
    print_colored("方法2: 手动启动Chrome", "green")
    print_colored("  1. 完全关闭所有Chrome窗口", "white")
    print_colored("  2. 按Win+R，输入cmd，按回车", "white")
    print_colored("  3. 复制粘贴以下命令并按回车：", "white")
    print_colored('     chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\\chrome_debug"', "yellow")
    print_colored("", "white")
    print_colored("方法3: 如果Chrome安装在其他位置", "green")
    print_colored('  "完整Chrome路径" --remote-debugging-port=9222 --user-data-dir="%TEMP%\\chrome_debug"', "yellow")
    print_colored("", "white")
    print_colored("启动成功后，Chrome会打开一个新窗口，请在其中登录网站", "blue")

async def main():
    """主测试函数"""
    print_colored("Chrome浏览器连接测试工具", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 测试调试端口
    port_ok = await test_chrome_debug_port()
    
    if port_ok:
        # 测试Playwright连接
        playwright_ok = await test_playwright_connection()
        
        if playwright_ok:
            print_colored("\n🎉 Chrome连接测试全部通过！", "green")
            print_colored("您现在可以运行主程序了：python main.py", "green")
            return True
        else:
            print_colored("\n⚠️ Chrome调试端口正常，但Playwright连接失败", "yellow")
    else:
        print_colored("\n❌ Chrome调试端口连接失败", "red")
    
    # 显示帮助信息
    print_chrome_startup_help()
    return False

if __name__ == "__main__":
    success = asyncio.run(main())
    print_colored("\n按任意键退出...", "yellow")
    input()
    
    if success:
        # 询问是否直接运行主程序
        run_main = input("\n是否现在运行主程序？(y/N): ").strip().lower()
        if run_main in ['y', 'yes', '是']:
            import subprocess
            subprocess.run(["python", "main.py"])
