# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现浏览器连接和控制功能

"""
浏览器控制器模块
负责连接已登录的Chrome浏览器实例并进行页面操作
"""

import asyncio
from playwright.async_api import async_playwright, Browser, Page, BrowserContext
from typing import Optional
import json

from utils import setup_logger, random_delay, print_colored
import config

logger = setup_logger(__name__)

class BrowserController:
    """浏览器控制器类"""
    
    def __init__(self):
        """初始化浏览器控制器"""
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.logger = logger
        
    async def connect_to_existing_chrome(self) -> bool:
        """
        连接到已存在的Chrome浏览器实例
        
        Returns:
            连接是否成功
        """
        try:
            self.playwright = await async_playwright().start()
            
            # 连接到现有的Chrome浏览器实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                config.CHROME_DEBUG_URL
            )
            
            # 获取现有的浏览器上下文
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                self.context = await self.browser.new_context()
            
            # 获取现有页面或创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]
            else:
                self.page = await self.context.new_page()
            
            print_colored("✓ 成功连接到Chrome浏览器", "green")
            self.logger.info("成功连接到Chrome浏览器")
            return True
            
        except Exception as e:
            print_colored(f"✗ 连接Chrome浏览器失败: {e}", "red")
            self.logger.error(f"连接Chrome浏览器失败: {e}")
            return False
    
    async def navigate_to_target_page(self, url: str) -> bool:
        """
        导航到目标页面
        
        Args:
            url: 目标URL
            
        Returns:
            导航是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            print_colored(f"正在导航到: {url}", "blue")
            await self.page.goto(url, wait_until="networkidle")
            
            # 等待页面加载完成
            await random_delay(config.MIN_DELAY, config.MAX_DELAY)
            
            current_url = self.page.url
            print_colored(f"✓ 成功导航到页面: {current_url}", "green")
            self.logger.info(f"成功导航到页面: {current_url}")
            return True
            
        except Exception as e:
            print_colored(f"✗ 导航失败: {e}", "red")
            self.logger.error(f"导航失败: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: int = 30000) -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（毫秒）
            
        Returns:
            元素是否出现
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.wait_for_selector(selector, timeout=timeout)
            self.logger.info(f"元素已出现: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"等待元素失败: {selector}, 错误: {e}")
            return False
    
    async def click_element(self, selector: str) -> bool:
        """
        点击页面元素
        
        Args:
            selector: 元素选择器
            
        Returns:
            点击是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.click(selector)
            await random_delay(1, 2)  # 点击后短暂延迟
            
            self.logger.info(f"成功点击元素: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"点击元素失败: {selector}, 错误: {e}")
            return False
    
    async def fill_input(self, selector: str, value: str) -> bool:
        """
        填充输入框
        
        Args:
            selector: 输入框选择器
            value: 要填充的值
            
        Returns:
            填充是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.fill(selector, value)
            await random_delay(0.5, 1)  # 填充后短暂延迟
            
            self.logger.info(f"成功填充输入框: {selector} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"填充输入框失败: {selector}, 错误: {e}")
            return False
    
    async def get_page_title(self) -> str:
        """
        获取页面标题
        
        Returns:
            页面标题
        """
        try:
            if not self.page:
                return ""
            
            title = await self.page.title()
            return title
            
        except Exception as e:
            self.logger.error(f"获取页面标题失败: {e}")
            return ""
    
    async def close(self):
        """关闭浏览器连接"""
        try:
            if self.playwright:
                await self.playwright.stop()
                print_colored("✓ 浏览器连接已关闭", "yellow")
                self.logger.info("浏览器连接已关闭")
                
        except Exception as e:
            self.logger.error(f"关闭浏览器连接失败: {e}")
