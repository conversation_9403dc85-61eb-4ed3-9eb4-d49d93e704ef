@echo off
REM 版本号: v1.0.0
REM 变更记录: 
REM - v1.0.0: 初始版本，提供简易启动脚本

echo ===================================
echo    自动化数据导出工具启动脚本
echo ===================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
pip show playwright >nul 2>&1
if %errorlevel% neq 0 (
    echo [提示] 正在安装依赖...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
    playwright install chromium
)

echo.
echo [步骤1] 启动Chrome浏览器（远程调试模式）
echo.
echo 是否需要自动启动Chrome浏览器？
echo 1. 是，自动启动（推荐）
echo 2. 否，我已手动启动
echo.
set /p choice="请选择 [1/2]: "

if "%choice%"=="1" (
    echo.
    echo 正在启动Chrome浏览器...
    
    REM 创建临时目录
    if not exist "%TEMP%\chrome_debug" mkdir "%TEMP%\chrome_debug"
    
    REM 尝试启动Chrome
    start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug"
    if %errorlevel% neq 0 (
        start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug"
    )
    
    echo.
    echo [提示] 请在打开的Chrome浏览器中登录网站
    echo 网址: https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1^&pageSize=10
    echo.
    echo 登录完成后，请按任意键继续...
    pause >nul
)

echo.
echo [步骤2] 启动自动化导出工具
echo.
echo 正在启动...
echo.

python main.py

echo.
if %errorlevel% neq 0 (
    echo [错误] 程序异常退出，请查看上方错误信息
) else (
    echo [成功] 程序正常结束
)

echo.
echo 按任意键退出...
pause >nul
