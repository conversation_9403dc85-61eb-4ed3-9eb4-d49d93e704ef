# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，定义基础配置参数

"""
自动化数据导出工具配置文件
"""

# 目标网站配置
TARGET_URL = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10"

# Chrome浏览器配置
CHROME_DEBUG_PORT = 9222
CHROME_DEBUG_URL = f"http://localhost:{CHROME_DEBUG_PORT}"

# 时间配置
EXPORT_YEAR = 2024  # 要导出的年份
MONTHS_PER_EXPORT = 2  # 每次导出的月份数量（不超过3个月限制）

# 操作延迟配置（秒）
MIN_DELAY = 2  # 最小延迟
MAX_DELAY = 5  # 最大延迟
EXPORT_WAIT_TIMEOUT = 300  # 导出等待超时时间（5分钟）

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 10  # 重试间隔（秒）
