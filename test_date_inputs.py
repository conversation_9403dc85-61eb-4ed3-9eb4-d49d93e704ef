# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 测试新的日期输入方法

import asyncio
from browser_controller import BrowserController
from export_handler import ExportHandler
from utils import print_colored
import config

async def test_date_input_elements():
    """测试页面上的日期输入元素"""
    print_colored("=== 测试日期输入元素识别 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        # 连接浏览器
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("✗ 无法连接Chrome浏览器", "red")
            print_colored("请确保Chrome已启动（调试模式）", "yellow")
            return False
        
        # 导航到目标页面
        if not await browser_controller.navigate_to_target_page(config.TARGET_URL):
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        print_colored("✓ 成功连接并导航到页面", "green")
        
        # 测试各种日期输入选择器
        selectors_to_test = [
            ("精确开始日期", "input[placeholder='开始日期'][date-range='start']"),
            ("精确结束日期", "input[placeholder='结束日期'][date-range='end']"),
            ("提交时间容器", "#submitTime"),
            ("日期范围选择器", ".ant-picker-range"),
            ("备选开始日期", "input[placeholder*='开始']"),
            ("备选结束日期", "input[placeholder*='结束']"),
            ("所有日期输入", "input[date-range]"),
            ("Ant Design选择器", ".ant-picker input")
        ]
        
        found_elements = 0
        for name, selector in selectors_to_test:
            try:
                elements = await browser_controller.page.query_selector_all(selector)
                if elements:
                    print_colored(f"✓ {name}: 找到 {len(elements)} 个元素", "green")
                    found_elements += 1
                    
                    # 显示元素的详细信息
                    for i, element in enumerate(elements[:2]):  # 只显示前2个
                        try:
                            placeholder = await element.get_attribute("placeholder")
                            date_range = await element.get_attribute("date-range")
                            element_id = await element.get_attribute("id")
                            print_colored(f"  元素 {i+1}: placeholder='{placeholder}', date-range='{date_range}', id='{element_id}'", "blue")
                        except:
                            pass
                else:
                    print_colored(f"✗ {name}: 未找到", "yellow")
            except Exception as e:
                print_colored(f"✗ 测试 {name} 时出错: {e}", "red")
        
        print_colored(f"\n总结: 找到 {found_elements}/{len(selectors_to_test)} 种类型的元素", "blue")
        
        return found_elements > 0
        
    except Exception as e:
        print_colored(f"✗ 测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

async def test_date_setting():
    """测试日期设置功能"""
    print_colored("\n=== 测试日期设置功能 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        # 连接浏览器
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("✗ 无法连接Chrome浏览器", "red")
            return False
        
        # 导航到目标页面
        if not await browser_controller.navigate_to_target_page(config.TARGET_URL):
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        # 创建导出处理器
        export_handler = ExportHandler(browser_controller)
        
        # 测试设置日期范围
        test_start_date = "2025-01-01"
        test_end_date = "2025-01-31"
        
        print_colored(f"测试设置日期范围: {test_start_date} 至 {test_end_date}", "blue")
        
        success = await export_handler.set_date_range(test_start_date, test_end_date)
        
        if success:
            print_colored("✓ 日期设置测试成功", "green")
            
            # 验证日期是否正确设置
            await asyncio.sleep(2)  # 等待页面更新
            
            # 尝试读取输入框的值来验证
            try:
                start_input = await browser_controller.page.query_selector("input[placeholder='开始日期'][date-range='start']")
                end_input = await browser_controller.page.query_selector("input[placeholder='结束日期'][date-range='end']")
                
                if start_input and end_input:
                    start_value = await start_input.get_attribute("value")
                    end_value = await end_input.get_attribute("value")
                    print_colored(f"验证结果: 开始日期='{start_value}', 结束日期='{end_value}'", "blue")
                    
                    if start_value == test_start_date and end_value == test_end_date:
                        print_colored("✓ 日期验证成功，值完全匹配", "green")
                        return True
                    else:
                        print_colored("⚠️ 日期值不完全匹配，但设置过程成功", "yellow")
                        return True
                else:
                    print_colored("⚠️ 无法验证日期值，但设置过程成功", "yellow")
                    return True
                    
            except Exception as e:
                print_colored(f"验证过程出错: {e}", "yellow")
                return True  # 设置成功，验证失败不影响结果
        else:
            print_colored("✗ 日期设置测试失败", "red")
            return False
        
    except Exception as e:
        print_colored(f"✗ 测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

async def main():
    """主测试函数"""
    print_colored("日期输入功能测试工具", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 测试元素识别
    elements_ok = await test_date_input_elements()
    
    # 测试日期设置
    if elements_ok:
        setting_ok = await test_date_setting()
    else:
        setting_ok = False
    
    # 总结
    print_colored("\n" + "=" * 50, "cyan")
    print_colored("测试总结", "cyan")
    print_colored("=" * 50, "cyan")
    
    if elements_ok and setting_ok:
        print_colored("🎉 所有测试通过！日期输入功能正常", "green")
        print_colored("现在可以运行完整的导出程序: python main.py", "green")
    elif elements_ok:
        print_colored("⚠️ 元素识别成功，但日期设置可能有问题", "yellow")
        print_colored("建议检查页面状态或网站结构变化", "yellow")
    else:
        print_colored("❌ 元素识别失败，可能需要更新选择器", "red")
        print_colored("请检查网站页面结构是否发生变化", "red")
    
    print_colored("\n按任意键退出...", "blue")
    input()

if __name__ == "__main__":
    asyncio.run(main())
