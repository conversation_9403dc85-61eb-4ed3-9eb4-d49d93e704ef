# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 测试新配置（2025年，每月1次导出）

import asyncio
from time_generator import TimeRangeGenerator
from browser_controller import BrowserController
from utils import print_colored
import config

async def test_new_configuration():
    """测试新的配置设置"""
    print_colored("=== 测试新配置 ===", "cyan")
    
    # 测试时间范围生成
    print_colored(f"目标年份: {config.EXPORT_YEAR}", "blue")
    print_colored(f"每次导出月份数: {config.MONTHS_PER_EXPORT}", "blue")
    
    generator = TimeRangeGenerator(config.EXPORT_YEAR, config.MONTHS_PER_EXPORT)
    time_ranges = generator.generate_time_ranges()
    
    print_colored(f"\n生成了 {len(time_ranges)} 个时间范围:", "green")
    for i, (start, end) in enumerate(time_ranges[:5]):  # 只显示前5个
        description = generator.get_range_description(start, end)
        print_colored(f"  {i+1:2d}. {description} ({start} 至 {end})", "white")
    
    if len(time_ranges) > 5:
        print_colored(f"  ... 还有 {len(time_ranges) - 5} 个时间范围", "white")
    
    # 测试浏览器连接（如果Chrome正在运行）
    print_colored("\n=== 测试浏览器连接 ===", "cyan")
    browser_controller = BrowserController()
    
    try:
        if await browser_controller.connect_to_existing_chrome():
            print_colored("✓ 浏览器连接成功", "green")
            
            title = await browser_controller.get_page_title()
            print_colored(f"当前页面: {title}", "blue")
            
            return True
        else:
            print_colored("✗ 浏览器连接失败（Chrome可能未启动）", "yellow")
            return False
            
    except Exception as e:
        print_colored(f"✗ 连接测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

async def main():
    """主测试函数"""
    print_colored("新配置测试工具", "cyan")
    print_colored("=" * 50, "cyan")
    
    success = await test_new_configuration()
    
    print_colored("\n" + "=" * 50, "cyan")
    if success:
        print_colored("✓ 配置测试通过！", "green")
        print_colored("现在可以运行主程序: python main.py", "green")
    else:
        print_colored("⚠️ 部分测试失败，但时间范围生成正常", "yellow")
        print_colored("请确保Chrome浏览器已启动（调试模式）", "yellow")
    
    print_colored("\n按任意键退出...", "blue")
    input()

if __name__ == "__main__":
    asyncio.run(main())
