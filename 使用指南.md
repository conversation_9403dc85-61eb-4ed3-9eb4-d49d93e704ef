# 自动化数据导出工具 - 使用指南

## 版本信息
- 版本号: v1.0.0
- 最后更新: 2025-07-31

## 🎉 恭喜！工具已成功开发完成

根据测试结果，您的自动化数据导出工具已经可以正常工作了！

## ✅ 已验证的功能

1. **Chrome浏览器连接** - ✅ 成功
2. **页面导航** - ✅ 成功
3. **日期范围设置** - ✅ 成功识别并操作日期选择器
4. **导出按钮识别** - ✅ 成功找到并点击导出按钮
5. **确认对话框处理** - ✅ 成功处理确认对话框
6. **多时间段处理** - ✅ 正在按计划处理6个时间段

## 🚀 快速开始

### 方法一：一键启动（推荐）

1. **启动Chrome浏览器**：
   ```bash
   powershell -ExecutionPolicy Bypass -File start_chrome.ps1
   ```

2. **在Chrome中登录网站**：
   - 访问：https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10
   - 完成登录操作

3. **运行主程序**：
   ```bash
   python main.py
   ```

### 方法二：使用批处理脚本

1. 双击运行 `start.bat`
2. 按照提示操作

## 📊 工作流程

工具会自动执行以下步骤：

1. **连接浏览器** - 连接到已启动的Chrome调试实例
2. **导航页面** - 自动导航到数据页面
3. **生成时间范围** - 创建12个1个月的时间段：
   - 2025-01-01 至 2025-01-31
   - 2025-02-01 至 2025-02-28
   - 2025-03-01 至 2025-03-31
   - 2025-04-01 至 2025-04-30
   - 2025-05-01 至 2025-05-31
   - 2025-06-01 至 2025-06-30
   - 2025-07-01 至 2025-07-31
   - 2025-08-01 至 2025-08-31
   - 2025-09-01 至 2025-09-30
   - 2025-10-01 至 2025-10-31
   - 2025-11-01 至 2025-11-30
   - 2025-12-01 至 2025-12-31

4. **逐个导出** - 对每个时间段：
   - 设置日期范围
   - 点击导出按钮
   - 确认导出操作
   - 等待导出完成
   - 处理下一个时间段

## 🔧 实际测试结果

根据刚才的测试，工具已经成功：

- ✅ 识别了网站的日期范围选择器（`.ant-picker-range`）
- ✅ 正确填充了日期范围（格式：`2025-01-01` 至 `2025-01-31`）
- ✅ 找到了导出按钮并成功点击
- ✅ 处理了确认对话框
- ✅ 开始了自动化导出流程

## ⚠️ 重要提示

1. **保持浏览器开启** - 导出过程中请勿关闭Chrome浏览器
2. **网络稳定** - 确保网络连接稳定
3. **登录状态** - 确保在Chrome中已完成网站登录
4. **耐心等待** - 每个时间段的导出可能需要几分钟时间

## 🛠️ 故障排除

### 如果连接Chrome失败：
```bash
# 重新启动Chrome（调试模式）
powershell -ExecutionPolicy Bypass -File start_chrome.ps1
```

### 如果页面元素找不到：
- 检查网站页面是否已加载完成
- 确认已完成登录
- 网站结构可能已更改，需要更新选择器

### 如果导出失败：
- 检查网络连接
- 确认账户权限
- 查看Chrome浏览器中的实际操作

## 📈 性能预期

- **总时间段**: 12个（每个1个月）
- **预计总时间**: 约20-60分钟
- **成功率**: 根据网络和网站响应情况而定

## 🎯 下一步操作

1. **确保Chrome已启动**（调试模式）
2. **在Chrome中完成网站登录**
3. **运行主程序开始自动导出**：
   ```bash
   python main.py
   ```

## 📞 技术支持

如果遇到问题：

1. **查看日志** - 程序会输出详细的操作日志
2. **检查Chrome** - 确认浏览器状态和页面内容
3. **重新启动** - 重启Chrome和程序通常能解决大部分问题

---

**🎉 恭喜您！工具已准备就绪，可以开始自动化导出全年数据了！**
