# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 测试完整的导出流程

import asyncio
from browser_controller import BrowserController
from export_handler import ExportHandler
from utils import print_colored
import config

async def test_button_recognition():
    """测试按钮识别"""
    print_colored("=== 测试按钮识别 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        # 连接浏览器
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("✗ 无法连接Chrome浏览器", "red")
            return False
        
        # 导航到目标页面
        if not await browser_controller.navigate_to_target_page(config.TARGET_URL):
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        print_colored("✓ 成功连接并导航到页面", "green")
        
        # 测试按钮选择器
        buttons_to_test = [
            ("搜索按钮", "button[type='submit']:has-text('搜 索')"),
            ("搜索按钮(备选)", "button:has-text('搜索')"),
            ("搜索按钮(通用)", ".ant-btn-primary:has-text('搜 索')"),
            ("导出业务数据按钮", "button[type='button']:has-text('导出业务数据')"),
            ("导出业务数据按钮(备选)", "button:has-text('导出业务数据')"),
            ("所有搜索相关按钮", "button:has-text('搜')"),
            ("所有导出相关按钮", "button:has-text('导出')"),
            ("所有提交按钮", "button[type='submit']"),
            ("所有普通按钮", "button[type='button']")
        ]
        
        found_buttons = 0
        for name, selector in buttons_to_test:
            try:
                elements = await browser_controller.page.query_selector_all(selector)
                if elements:
                    print_colored(f"✓ {name}: 找到 {len(elements)} 个按钮", "green")
                    found_buttons += 1
                    
                    # 显示按钮的详细信息
                    for i, element in enumerate(elements[:2]):  # 只显示前2个
                        try:
                            button_text = await element.inner_text()
                            button_type = await element.get_attribute("type")
                            button_class = await element.get_attribute("class")
                            print_colored(f"  按钮 {i+1}: 文本='{button_text}', type='{button_type}'", "blue")
                        except:
                            pass
                else:
                    print_colored(f"✗ {name}: 未找到", "yellow")
            except Exception as e:
                print_colored(f"✗ 测试 {name} 时出错: {e}", "red")
        
        print_colored(f"\n总结: 找到 {found_buttons}/{len(buttons_to_test)} 种类型的按钮", "blue")
        
        return found_buttons > 0
        
    except Exception as e:
        print_colored(f"✗ 测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

async def test_single_month_flow():
    """测试单个月的完整流程"""
    print_colored("\n=== 测试单个月完整流程 ===", "cyan")
    
    browser_controller = BrowserController()
    
    try:
        # 连接浏览器
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("✗ 无法连接Chrome浏览器", "red")
            return False
        
        # 导航到目标页面
        if not await browser_controller.navigate_to_target_page(config.TARGET_URL):
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        # 创建导出处理器
        export_handler = ExportHandler(browser_controller)
        
        # 测试2025年1月的导出流程
        test_start_date = "2025-01-01"
        test_end_date = "2025-01-31"
        
        print_colored(f"测试导出流程: {test_start_date} 至 {test_end_date}", "blue")
        
        success = await export_handler.export_data_for_range(test_start_date, test_end_date)
        
        if success:
            print_colored("✓ 单个月导出流程测试成功", "green")
            return True
        else:
            print_colored("✗ 单个月导出流程测试失败", "red")
            return False
        
    except Exception as e:
        print_colored(f"✗ 测试失败: {e}", "red")
        return False
    finally:
        await browser_controller.close()

async def main():
    """主测试函数"""
    print_colored("完整导出流程测试工具", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 测试按钮识别
    buttons_ok = await test_button_recognition()
    
    # 如果按钮识别成功，测试完整流程
    if buttons_ok:
        print_colored("\n是否继续测试完整的导出流程？", "yellow")
        print_colored("注意: 这将实际执行一次导出操作", "yellow")
        user_input = input("继续测试？(y/N): ").strip().lower()
        
        if user_input in ['y', 'yes', '是']:
            flow_ok = await test_single_month_flow()
        else:
            print_colored("跳过完整流程测试", "blue")
            flow_ok = True  # 用户选择跳过，不算失败
    else:
        flow_ok = False
    
    # 总结
    print_colored("\n" + "=" * 50, "cyan")
    print_colored("测试总结", "cyan")
    print_colored("=" * 50, "cyan")
    
    if buttons_ok and flow_ok:
        print_colored("🎉 所有测试通过！", "green")
        print_colored("新的导出流程已准备就绪:", "green")
        print_colored("1. 设置日期范围 → 2. 点击搜索 → 3. 点击导出业务数据", "blue")
        print_colored("现在可以运行完整程序: python main.py", "green")
    elif buttons_ok:
        print_colored("⚠️ 按钮识别成功，但未测试完整流程", "yellow")
        print_colored("建议运行完整程序进行实际测试", "yellow")
    else:
        print_colored("❌ 按钮识别失败", "red")
        print_colored("请检查页面结构或按钮文本是否发生变化", "red")
    
    print_colored("\n按任意键退出...", "blue")
    input()

if __name__ == "__main__":
    asyncio.run(main())
